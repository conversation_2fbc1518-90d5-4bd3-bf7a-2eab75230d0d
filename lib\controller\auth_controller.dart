import 'dart:async';
import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:smsautoforwardapp/bnb.dart';
import 'package:smsautoforwardapp/controller/payment_controller.dart';
import 'package:smsautoforwardapp/login.dart';
import 'package:smsautoforwardapp/model/forward_all_email_model.dart';
import 'package:smsautoforwardapp/model/forward_all_url_model.dart';
import 'package:smsautoforwardapp/model/subscription_model.dart';
import 'package:smsautoforwardapp/widgets/premium_ui.dart';
import 'package:url_launcher/url_launcher.dart';
import '../background_service.dart';
import 'package:http/http.dart' as http;
import '../model/user.dart';
import '../style.dart';

class AuthController extends GetxController {
  static AuthController instance = Get.find();
  Rx<bool> isAuthUpdating = false.obs;
  Rx<int> endTime = 0.obs;

  late Rx<User?> _user;
  bool isLoging = false;
  User? _tempUser; // Temporary user for upgrade flow
  User? get user => _tempUser ?? _user.value;
  final _auth = FirebaseAuth.instance;
  RxList<UserModel> allUsersList = <UserModel>[].obs;

  Rx<int> isObscure = 1.obs;

  @override
  void onReady() {
    super.onReady();
    _user = Rx<User?>(_auth.currentUser);
    _user.bindStream(_auth.authStateChanges());
    ever(_user, loginRedirect);
  }

  @override
  void onClose() {
    super.onReady();
  }

  loginRedirect(var user) async {
    Timer(Duration(seconds: isLoging ? 0 : 2), () {
      if (_auth.currentUser == null) {
        isLoging = false;
        Get.offAll(() => const Login());
      } else {
        isLoging = true;
        sendAuthStateToAndroid(_auth.currentUser!.uid);
        Get.offAll(() => const BNB());
      }
    });
  }

  void showDeviceLimitDialog({User? currentUser}) {
    Get.dialog(
      barrierDismissible: false,
      PopScope(
        canPop: false,
        child: AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.devices, color: Colors.orange),
              const SizedBox(width: 8),
              const Text('Device Limit Reached'),
            ],
          ),
          content: const Text(
              'Your current subscription plan allows only 1 device support. To use this device on multiple devices, upgrade to Elite Plan or logout from your previous device.'),
          actions: [
            TextButton(
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.grey),
              ),
              onPressed: () async {
                Get.back();
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: maincolor,
              ),
              child: const Text(
                'Upgrade to Elite',
                style: TextStyle(color: Colors.white),
              ),
              onPressed: () {
                Get.back();
                _showUpgradeScreen(currentUser: currentUser);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showUpgradeScreen({User? currentUser}) {
    // Ensure PaymentController is available
    if (!Get.isRegistered<PaymentController>()) {
      Get.lazyPut(() => PaymentController());
    }

    // If we have a current user, temporarily restore auth state for payment
    if (currentUser != null) {
      // Create a temporary auth state for payment processing
      _tempUser = currentUser;
      print('Flutter: Temp user set for upgrade flow: ${_tempUser!.uid}');
    }

    // Show a modal bottom sheet with upgrade options, focused on Elite Plan
    Get.bottomSheet(
      Container(
        height: Get.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Upgrade Required',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        onPressed: () async {
                          Get.back();
                          _clearTempUser();
                        },
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  const Text(
                    'Upgrade to Elite Plan to use multiple devices',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            // Premium UI
            const Expanded(
              child: Premium(
                isModifyingSubscription: false,
                forceElitePlan: true,
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
    );
  }

  void _clearTempUser() {
    _tempUser = null;
  }

  User? get tempUser {
    print(
        'Flutter: tempUser getter called, value: ${_tempUser?.uid ?? 'null'}');
    return _tempUser;
  }

  Future<void> _reAuthenticateAfterUpgrade(User tempUser) async {
    try {
      // Update the user's login status in Firestore
      await firestore.collection('users').doc(tempUser.uid).update({
        'isAlreadyLoggedIn': true,
      });

      // Send auth state to Android for background service
      await sendAuthStateToAndroid(tempUser.uid);

      print('Flutter: User re-authenticated after Elite upgrade');
    } catch (e) {
      print('Flutter: Error re-authenticating user: $e');
    }
  }

  void googleLogin() async {
    String errorMessage = '';
    final GoogleSignIn googleSignIn = GoogleSignIn();
    isAuthUpdating.value = true;
    isLoging = true;

    googleSignIn.disconnect();

    try {
      final GoogleSignInAccount? googleSignInAccount =
          await googleSignIn.signIn();
      if (googleSignInAccount != null) {
        final GoogleSignInAuthentication googleAuth =
            await googleSignInAccount.authentication;
        final crendentials = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        final UserCredential userCredential =
            await _auth.signInWithCredential(crendentials);

        User? registereduser = userCredential.user;

        UserModel user = UserModel(
            name: registereduser!.displayName ?? '',
            email: registereduser.email,
            stripeCustomerID: '',
            noOfForwardsused: 0,
            noOfForwardsPerMonth: 50,
            createdAt: Timestamp.fromMillisecondsSinceEpoch(
                DateTime.now().millisecondsSinceEpoch),
            uid: registereduser.uid,
            isPremium: false,
            isAlreadyLoggedIn: false, // Default to false for new users
            currentSubscription: SubsriptionModel(
                subscriptionItem: '',
                subscriptionID: '',
                subscriptionPlan: '',
                planDescription: ''),
            forwardAllEmail:
                ForwaredAllEmailModelClass(isActive: false, recipients: ['']),
            forwardAllUrl: ForwaredAllURLModelClass(
                isActive: false, method: '', url: '', jsonBody: ''));

        final userDocRef =
            firestore.collection('users').doc(registereduser.uid);
        if (!(await userDocRef.get()).exists) {
          await firestore
              .collection('users')
              .doc(registereduser.uid)
              .set(user.toJson());
        }

        // Check subscription plan and login status
        final userDoc =
            await firestore.collection('users').doc(registereduser.uid).get();
        if (userDoc.exists) {
          final userData = userDoc.data() as Map<String, dynamic>;
          final subscriptionPlan =
              userData['currentSubscription']?['subscriptionPlan'] ?? '';
          final isAlreadyLoggedIn = userData['isAlreadyLoggedIn'] ?? false;

          if (subscriptionPlan != 'Elite Plan' && isAlreadyLoggedIn) {
            // Store user data before stopping service for upgrade flow
            final currentUser = registereduser;
            await stopService();
            await Future.delayed(const Duration(seconds: 3));
            showDeviceLimitDialog(currentUser: currentUser);
            return;
          } else if (subscriptionPlan != 'Elite Plan') {
            // If not Elite Plan and not already logged in, set isAlreadyLoggedIn to true
            await firestore
                .collection('users')
                .doc(registereduser.uid)
                .update({'isAlreadyLoggedIn': true});
          }
        }

        getSuccessSnackBar("successfully logged in");
      }
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case "account-exists-with-different-credential":
          errorMessage =
              "An account already exists with the same email address but different sign-in credentials.";
          break;
        case "invalid-email":
          errorMessage = "Invalid email";
          break;
        case "network-request-failed":
          errorMessage = "There is no Internet connection";
          break;
        case "email-already-in-use":
          errorMessage = "The account already exists for that email.";
          break;
        case "user-disabled":
          errorMessage = "User is currently disabled";
          break;
        case "user-not-found":
          errorMessage = "User not found";
          break;
        case "wrong-password":
          errorMessage = "Wrong password";
          break;
        default:
          errorMessage = "Login Failed!";
          break;
      }
      getErrorSnackBar(errorMessage);
    } finally {
      isAuthUpdating.value = false;
    }
  }

  void signInWithFacebook() async {
    String errorMessage = '';
    isAuthUpdating.value = true;
    isLoging = true;

    try {
      final LoginResult result = await FacebookAuth.instance
          .login(permissions: (['email', 'public_profile']));
      final token = result.accessToken!.token;

      final graphResponse = await http.get(Uri.parse(
          'https://graph.facebook.com/'
          'v2.12/me?fields=name,first_name,last_name,email&access_token=$token'));

      jsonDecode(graphResponse.body);

      final AuthCredential facebookCredential =
          FacebookAuthProvider.credential(result.accessToken!.token);
      final userCredential =
          await FirebaseAuth.instance.signInWithCredential(facebookCredential);
      User? registereduser = userCredential.user;

      UserModel user = UserModel(
          name: registereduser!.displayName ?? '',
          email: registereduser.email,
          createdAt: Timestamp.fromMillisecondsSinceEpoch(
              DateTime.now().millisecondsSinceEpoch),
          noOfForwardsused: 0,
          noOfForwardsPerMonth: 50,
          stripeCustomerID: '',
          uid: registereduser.uid,
          isPremium: false,
          isAlreadyLoggedIn: false, // Default to false for new users
          currentSubscription: SubsriptionModel(
              subscriptionItem: '',
              subscriptionID: '',
              subscriptionPlan: '',
              planDescription: ''),
          forwardAllEmail:
              ForwaredAllEmailModelClass(isActive: false, recipients: ['']),
          forwardAllUrl: ForwaredAllURLModelClass(
              isActive: false, method: '', url: '', jsonBody: ''));
      final userDocRef = firestore.collection('users').doc(registereduser.uid);
      if (!(await userDocRef.get()).exists) {
        await firestore
            .collection('users')
            .doc(registereduser.uid)
            .set(user.toJson());
      }

      // Check subscription plan and login status
      final userDoc =
          await firestore.collection('users').doc(registereduser.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final subscriptionPlan =
            userData['currentSubscription']?['subscriptionPlan'] ?? '';
        final isAlreadyLoggedIn = userData['isAlreadyLoggedIn'] ?? false;

        if (subscriptionPlan != 'Elite Plan' && isAlreadyLoggedIn) {
          // Store user data before stopping service for upgrade flow
          final currentUser = registereduser;
          await stopService();
          await Future.delayed(const Duration(seconds: 3));
          showDeviceLimitDialog(currentUser: currentUser);
        } else if (subscriptionPlan != 'Elite Plan') {
          // If not Elite Plan and not already logged in, set isAlreadyLoggedIn to true
          await firestore
              .collection('users')
              .doc(registereduser.uid)
              .update({'isAlreadyLoggedIn': true});
        }
      }

      getSuccessSnackBar("successfully logged in");
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case "account-exists-with-different-credential":
          errorMessage =
              "An account already exists with the same email address but different sign-in credentials.";
          break;
        case "invalid-email":
          errorMessage = "Invalid email";
          break;
        case "network-request-failed":
          errorMessage = "There is no Internet connection";
          break;
        case "email-already-in-use":
          errorMessage = "The account already exists for that email.";
          break;
        case "user-disabled":
          errorMessage = "User is currently disabled";
          break;
        case "user-not-found":
          errorMessage = "User not found";
          break;
        case "wrong-password":
          errorMessage = "Wrong password";
          break;
        default:
          errorMessage = "Login Failed!";
          break;
      }
      getErrorSnackBar(errorMessage);
    } finally {
      isAuthUpdating.value = false;
    }
  }

  updateUserSubscription(
      {required bool isPremiumUser,
      required int noOfForwardsPerMonth,
      required SubsriptionModel subsriptionModel}) async {
    // Use temp user ID if available, otherwise use current user ID
    final userId = _tempUser?.uid ?? user!.uid;

    await firestore.collection('users').doc(userId).update({
      'isPremium': isPremiumUser,
      'noOfForwardsPerMonth': noOfForwardsPerMonth,
      'currentSubscription': subsriptionModel.toJson()
    });

    // If we were using temp user, we need to re-authenticate the user
    if (_tempUser != null) {
      // Re-authenticate the user after successful Elite upgrade
      await _reAuthenticateAfterUpgrade(_tempUser!);
    }

    // Clear temp user after successful subscription update
    _clearTempUser();
  }

  updateSubscriptionEndsAt({required DateTime? subscriptionEndsAt}) async {
    await firestore.collection('users').doc(user!.uid).update({
      'subscriptionEndsAt': subscriptionEndsAt != null
          ? Timestamp.fromDate(subscriptionEndsAt)
          : null,
    });
  }

  updateStripeCustomerId({
    required String id,
  }) async {
    await firestore.collection('users').doc(user!.uid).update({
      'stripeCustomerID': id,
    });
  }

  signOut() async {
    final user = FirebaseAuth.instance.currentUser;

    if (user != null) {
      // Update isAlreadyLoggedIn to false and FCM token to null before sign out
      await FirebaseFirestore.instance.collection('users').doc(user.uid).set({
        'isAlreadyLoggedIn': false,
        'fcmToken': null,
      }, SetOptions(merge: true));
    }

    await stopService();
  }

  stopService() async {
    await stopBackgroundService();

    await _auth.signOut();

    // Only delete all controllers if we don't have a temp user (upgrade flow)
    if (_tempUser == null) {
      await Get.deleteAll();
    }
  }

  void checkForUpdate() async {
    final info = await PackageInfo.fromPlatform();
    final currentVersion = info.version;

    final doc = await FirebaseFirestore.instance
        .collection('config')
        .doc('version')
        .get();
    final latestVersion = doc['latestVersion'];
    final forceUpdate = doc['forceUpdate'];
    final updateUrl = doc['updateUrl'];
    final updateDialogText = doc['updateDialogText'];

    if (currentVersion != latestVersion && forceUpdate == true) {
      // Only sign out if user is currently logged in
      if (_auth.currentUser != null) {
        await signOut();
        await Future.delayed(const Duration(seconds: 3));
      }
      Get.dialog(
          barrierDismissible: false,
          PopScope(
            canPop: false,
            child: AlertDialog(
              title: const Text('Update Required'),
              content: Text(updateDialogText),
              actions: [
                ElevatedButton(
                  child: const Text(
                    'Update Now',
                    style: TextStyle(color: Colors.white),
                  ),
                  onPressed: () {
                    launchUrl(Uri.parse(updateUrl));
                  },
                ),
              ],
            ),
          ));
    }
  }
}
